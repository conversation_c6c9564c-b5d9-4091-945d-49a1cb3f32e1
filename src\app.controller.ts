import {
  <PERSON>,
  Get,
  MessageEvent,
  Param,
  Query,
  <PERSON>s,
  Sse,
} from '@nestjs/common';
import { AppService } from './app.service';
import { Response } from 'express';
import { readFileSync } from 'fs';
import { join } from 'path';
import { cwd } from 'process';
import { interval, map, Observable, of } from 'rxjs';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  index(@Res() response: Response) {
    const index = readFileSync(join(cwd(), 'public/index.html')).toString();
    response.type('text/html').send(index);
  }

  @Sse('/api/v1/sse')
  progress(@Query('streamId') streamId: string): Observable<MessageEvent> {
    console.log('🚀 ~ AppController ~ progress ~ streamId:', streamId);
    return this.appService.processLongTask(streamId);
  }

  @Sse('/api/v1/sse2')
  progress2(): Observable<MessageEvent> {
    return interval(1000).pipe(
      map((_) => {
        return {
          data: {
            progress: 100,
            description: 'This is a test',
          },
          id: '1',
        };
      }),
    );
  }
}
