# NestJS Server-Sent Events

The **NestJS Server-Sent Events** repository is a practical resource that demonstrates the integration of Server-Sent Events (SSE) with NestJS. SSE is a technology that enables real-time communication between a server and a client. This repository provides a straightforward approach to implementing SSE in your NestJS applications.

https://github.com/diegodario88/server-sent-events/assets/25825145/80e8f658-0ccb-4f52-873e-25e6ef84e73b

## Key Features

- **NestJS Integration:** Learn how to seamlessly integrate SSE functionality into your NestJS application. The repository includes well-structured code examples and best practices for a smooth implementation.

- **Real-time Communication:** Experience the power of real-time communication with SSE. Establish and maintain a persistent connection between the server and clients, enabling instant updates.

- **Event-Driven Architecture:** Explore how SSE fits into event-driven architecture. Define and handle custom events, allowing clients to subscribe to specific event types and receive targeted updates.

- **Scalability and Performance:** Discover techniques for optimizing scalability and performance. Learn how to efficiently manage multiple clients and minimize server resource usage.

## Installation

```bash
$ npm install
```

## Running the app

```bash
# development mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

> Access `http://localhost:3000` to see the client

## Running with PM2

PM2 is a production process manager for Node.js applications. This project includes PM2 configuration for easy deployment and management.

### Prerequisites

First, build the application:

```bash
$ npm run build
```

### PM2 Commands

```bash
# Start the application with PM2
$ npm run pm2:start

# Stop the application
$ npm run pm2:stop

# Restart the application
$ npm run pm2:restart

# Reload the application (zero-downtime restart)
$ npm run pm2:reload

# Delete the application from PM2
$ npm run pm2:delete

# Check application status
$ npm run pm2:status

# View logs
$ npm run pm2:logs

# Monitor application
$ npm run pm2:monit
```

### PM2 Configuration

The PM2 configuration is defined in `ecosystem.config.js`:

- **Application name:** server-sent-events
- **Script:** dist/main.js
- **Instances:** 1 (can be scaled)
- **Auto-restart:** enabled
- **Memory limit:** 1GB
- **Environment variables:** Configurable for development and production

## Contributing

Contributions are welcome! If you find a bug or want to add a new feature, please submit an issue or a pull request.

## License

This project is licensed under the [MIT License](LICENSE).
